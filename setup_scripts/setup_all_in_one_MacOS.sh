#!/bin/bash

# Cura并行开发环境一键安装脚本 - macOS版本
# 作者: AI Assistant
# 版本: 1.1
# 描述: 在已有Cura、Uranium、CuraEngine代码的目录中一键完成并行开发环境搭建

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 配置变量
PYTHON_VERSION="3.12"
CONAN_VERSION="2.7.0"
PROJECT_ROOT="$(pwd)"
VENV_NAME="Cura/cura_venv_MacOS"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 显示欢迎信息
show_welcome() {
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${CYAN}    Cura并行开发环境一键安装脚本 - macOS版本${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
    echo -e "${BLUE}本脚本将在当前目录完成以下操作:${NC}"
    echo "  1. 检查项目目录是否存在"
    echo "  2. 检查系统依赖"
    echo "  3. 在Cura文件夹内创建Python虚拟环境(cura_venv_MacOS)"
    echo "  4. 安装和配置Conan"
    echo "  5. 配置Uranium可编辑模式"
    echo "  6. 构建CuraEngine"
    echo "  7. 配置Cura开发环境"
    echo "  8. 验证配置"
    echo "  9. 生成PyCharm配置指导"
    echo ""
    echo -e "${YELLOW}注意: 请确保Cura、Uranium、CuraEngine目录已存在${NC}"
    echo -e "${YELLOW}首次运行可能需要30-60分钟，请保持网络连接${NC}"
    echo ""
    read -p "按Enter键继续，或Ctrl+C取消..."
    echo ""
}

# 检查项目目录
check_project_directories() {
    log_step "检查项目目录"
    
    local missing_dirs=()
    
    if [ ! -d "Cura" ]; then
        missing_dirs+=("Cura")
    else
        log_success "Cura目录存在"
    fi
    
    if [ ! -d "Uranium" ]; then
        missing_dirs+=("Uranium")
    else
        log_success "Uranium目录存在"
    fi
    
    if [ ! -d "CuraEngine" ]; then
        missing_dirs+=("CuraEngine")
    else
        log_success "CuraEngine目录存在"
    fi
    
    if [ ${#missing_dirs[@]} -gt 0 ]; then
        log_error "缺少以下项目目录: ${missing_dirs[*]}"
        log_error "请确保在包含Cura、Uranium、CuraEngine目录的根目录中运行此脚本"
        exit 1
    fi
    
    log_success "所有项目目录检查完成"
}

# 检查系统依赖
check_system_requirements() {
    log_step "检查系统依赖"
    
    # 检查macOS版本
    MACOS_VERSION=$(sw_vers -productVersion)
    log_info "macOS版本: $MACOS_VERSION"
    
    # 检查Xcode命令行工具
    if ! xcode-select -p &> /dev/null; then
        log_error "Xcode命令行工具未安装"
        log_info "请运行: xcode-select --install"
        exit 1
    fi
    log_success "Xcode命令行工具已安装"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装"
        log_info "请安装Python 3.12或更高版本"
        exit 1
    fi
    
    PYTHON_VER=$(python3 --version | cut -d' ' -f2)
    log_info "Python版本: $PYTHON_VER"
    
    # 检查CMake
    if ! command -v cmake &> /dev/null; then
        log_error "CMake未安装"
        log_info "请运行: brew install cmake"
        exit 1
    fi
    
    CMAKE_VER=$(cmake --version | head -1 | cut -d' ' -f3)
    log_info "CMake版本: $CMAKE_VER"
    
    # 检查Ninja
    if ! command -v ninja &> /dev/null; then
        log_error "Ninja未安装"
        log_info "请运行: brew install ninja"
        exit 1
    fi
    
    NINJA_VER=$(ninja --version)
    log_info "Ninja版本: $NINJA_VER"
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装"
        exit 1
    fi
    
    log_success "系统依赖检查完成"
}

# 创建Python虚拟环境
create_virtual_environment() {
    log_step "在Cura文件夹内创建Python虚拟环境(cura_venv_MacOS)"

    # 确保Cura目录存在
    if [ ! -d "Cura" ]; then
        log_error "Cura目录不存在"
        exit 1
    fi

    if [ -d "$VENV_NAME" ]; then
        log_warning "虚拟环境已存在，跳过创建"
    else
        python3 -m venv "$VENV_NAME"
        log_success "虚拟环境创建完成: $VENV_NAME"
    fi

    # 激活虚拟环境
    source "$VENV_NAME/bin/activate"
    log_success "虚拟环境已激活"

    # 升级pip
    pip install --upgrade pip
    log_success "pip已升级"
}

# 安装和配置Conan
setup_conan() {
    log_step "安装和配置Conan"
    
    # 安装Conan
    pip install conan==$CONAN_VERSION
    log_success "Conan $CONAN_VERSION 安装完成"
    
    # 配置Conan
    conan config install https://github.com/ultimaker/conan-config.git
    log_success "Conan配置安装完成"
    
    # 检测profile
    conan profile detect --force
    log_success "Conan profile检测完成"
    
    # 移除需要认证的远程仓库
    conan remote remove cura 2>/dev/null || true
    log_info "已移除cura远程仓库（如果存在）"
    
    # 安装GitPython（Cura需要）
    pip install GitPython
    log_success "GitPython安装完成"
}

# 配置Uranium可编辑模式
setup_uranium_editable() {
    log_step "配置Uranium可编辑模式"
    
    cd Uranium
    
    # 设置可编辑模式（关键：包含完整的user和channel）
    conan editable add . --name=uranium --version=5.11.0-alpha.0 --user=ultimaker --channel=testing
    log_success "Uranium可编辑模式配置完成"
    
    cd ..
}

# 构建CuraEngine
build_curaengine() {
    log_step "构建CuraEngine"
    
    cd CuraEngine
    
    # 安装依赖
    log_info "安装CuraEngine依赖..."
    conan install . --build=missing --update
    
    # 构建Release版本
    log_info "构建CuraEngine Release版本..."
    cmake --preset conan-release
    cmake --build --preset conan-release
    
    log_success "CuraEngine构建完成"
    
    cd ..
}

# 配置Cura开发环境
setup_cura_environment() {
    log_step "配置Cura开发环境"
    
    cd Cura
    
    # 安装Cura依赖（会自动使用可编辑模式的Uranium）
    log_info "安装Cura依赖（这可能需要较长时间）..."
    conan install . --build=missing -g VirtualPythonEnv -g PyCharmRunEnv
    
    log_success "Cura开发环境配置完成"
    
    cd ..
}

# 验证配置
verify_setup() {
    log_step "验证配置"
    
    # 检查可编辑包
    log_info "检查Uranium可编辑模式..."
    if conan editable list | grep -q "uranium/5.11.0-alpha.0@ultimaker/testing"; then
        log_success "Uranium可编辑模式配置正确"
    else
        log_error "Uranium可编辑模式配置失败"
        return 1
    fi
    
    # 检查Python导入路径
    log_info "验证Python导入路径..."
    cd Cura
    source build/generators/virtual_python_env.sh
    
    URANIUM_PATH=$(python -c "import UM; print(UM.__file__)" 2>/dev/null)
    if [[ "$URANIUM_PATH" == *"$PROJECT_ROOT/Uranium"* ]]; then
        log_success "Uranium正确加载自本地开发目录"
        log_info "路径: $URANIUM_PATH"
    else
        log_error "Uranium未从本地目录加载"
        log_error "当前路径: $URANIUM_PATH"
        return 1
    fi
    
    cd ..
    
    # 检查CuraEngine
    if [ -f "CuraEngine/build/Release/CuraEngine" ]; then
        log_success "CuraEngine可执行文件存在"
    else
        log_warning "CuraEngine可执行文件不存在"
    fi
    
    log_success "配置验证完成"
}

# 创建启动脚本
create_launch_scripts() {
    log_step "创建启动脚本"
    
    # 创建Cura启动脚本
    cat > start_cura.sh << 'EOF'
#!/bin/bash
cd Cura
source build/generators/virtual_python_env.sh
python cura_app.py "$@"
EOF
    chmod +x start_cura.sh
    
    # 创建外部后端模式启动脚本
    cat > start_cura_external.sh << 'EOF'
#!/bin/bash
cd Cura
source build/generators/virtual_python_env.sh
echo "启动Cura（外部后端模式）..."
echo "请在另一个终端运行 ./start_curaengine.sh"
python cura_app.py --external-backend "$@"
EOF
    chmod +x start_cura_external.sh
    
    # 创建CuraEngine启动脚本
    cat > start_curaengine.sh << 'EOF'
#!/bin/bash
cd CuraEngine
echo "连接到Cura（端口49674）..."
./build/Release/CuraEngine connect 127.0.0.1:49674
EOF
    chmod +x start_curaengine.sh
    
    log_success "启动脚本创建完成"
}

# 生成PyCharm配置指导
generate_pycharm_guide() {
    log_step "生成PyCharm配置指导"

    local pycharm_guide_file="setup_scripts/PyCharm_MacOS_配置指导.md"

    cat > "$pycharm_guide_file" << EOF
# PyCharm MacOS 配置指导

## 项目结构
\`\`\`
$(pwd)/
├── Cura/
│   ├── cura_venv_MacOS/        # Python虚拟环境(MacOS专用)
│   │   ├── bin/
│   │   │   ├── python          # 主要Python解释器(推荐使用)
│   │   │   └── python3.12      # 直接Python解释器
│   │   └── lib/
│   └── build/generators/       # Conan生成的环境文件
├── Uranium/                    # Uranium框架（可编辑模式）
└── CuraEngine/                 # CuraEngine切片引擎
\`\`\`

## PyCharm配置步骤

### 1. 打开项目
1. 启动PyCharm
2. 选择 "Open" 或 "Open or Import"
3. 选择项目根目录: \`$(pwd)\`

### 2. 配置Python解释器
1. 打开 **PyCharm > Preferences** (macOS) 或 **File > Settings** (Windows/Linux)
2. 导航到 **Project > Python Interpreter**
3. 点击右上角的 **齿轮图标** > **Add...**
4. 选择 **Existing environment**
5. 设置解释器路径为:
   \`\`\`
   $(pwd)/Cura/cura_venv_MacOS/bin/python
   \`\`\`
   **注意**: 使用 \`python\` 而不是 \`python3.12\` 以避免动态库问题
6. 点击 **OK**

### 3. 配置项目根目录
1. 在 **Project > Python Interpreter** 页面
2. 点击 **Show All...** (如果有多个解释器)
3. 选择刚配置的解释器，点击右侧的 **Show paths for the selected interpreter** 图标
4. 确保以下路径已添加:
   - \`$(pwd)/Cura\`
   - \`$(pwd)/Uranium\`
   - \`$(pwd)/Cura/build/generators\` (如果存在)

### 4. 配置运行配置
1. 点击 **Run > Edit Configurations...**
2. 点击 **+** > **Python**
3. 配置如下:
   - **Name**: Cura Development
   - **Script path**: \`$(pwd)/Cura/cura_app.py\`
   - **Python interpreter**: 选择刚配置的解释器
   - **Working directory**: \`$(pwd)/Cura\`
   - **Environment variables**:
     \`\`\`
     PYTHONPATH=$(pwd)/Uranium:$(pwd)/Cura
     \`\`\`

### 5. 验证配置
在PyCharm的Python Console中运行:
\`\`\`python
import UM
print("Uranium路径:", UM.__file__)
# 应该显示: $(pwd)/Uranium/UM/__init__.py

import cura
print("Cura路径:", cura.__file__)
# 应该显示: $(pwd)/Cura/cura/__init__.py
\`\`\`

## 常见问题解决

### 问题1: 动态库加载错误
如果遇到 \`libpython3.12.dylib\` 错误:
1. 使用 \`$(pwd)/Cura/cura_venv_MacOS/bin/python\` 而不是 \`python3.12\`
2. 运行修复脚本: \`./setup_scripts/fix_python_interpreter.sh\`

### 问题2: 模块导入错误
如果无法导入Uranium或Cura模块:
1. 检查PYTHONPATH环境变量
2. 确认Uranium可编辑模式: \`conan editable list\`
3. 重新激活虚拟环境: \`source $(pwd)/Cura/cura_venv_MacOS/bin/activate\`

### 问题3: 代码补全不工作
1. 确保PyCharm已索引完所有项目文件
2. 在 **File > Invalidate Caches and Restart** 清除缓存
3. 重新配置Python解释器

## 开发工作流

### 日常开发
1. 启动PyCharm
2. 确保使用正确的Python解释器
3. 直接在PyCharm中运行/调试Cura

### 修改Uranium代码
1. 在PyCharm中编辑 \`Uranium/\` 目录下的文件
2. 保存后重启Cura即可看到效果（无需重新安装）

### 修改CuraEngine代码
1. 编辑 \`CuraEngine/\` 目录下的文件
2. 在终端中重新编译:
   \`\`\`bash
   cd CuraEngine
   cmake --build --preset conan-release
   \`\`\`
3. 重启Cura

### 调试技巧
1. **断点调试**: 在PyCharm中设置断点，使用Debug模式运行
2. **日志查看**: 在PyCharm的Run窗口查看Cura输出
3. **变量检查**: 使用PyCharm的Variables窗口检查运行时状态

## 环境变量说明

### 虚拟环境激活
\`\`\`bash
source $(pwd)/Cura/cura_venv_MacOS/bin/activate
\`\`\`

### Conan环境激活
\`\`\`bash
cd Cura
source build/generators/virtual_python_env.sh
\`\`\`

## 故障排除

如果遇到任何问题:
1. 运行验证脚本: \`./setup_scripts/verify_setup.sh\`
2. 检查虚拟环境: \`which python\` (应该指向MacOS虚拟环境)
3. 重新运行安装脚本: \`./setup_scripts/setup_all_in_one_MacOS.sh\`

---
*配置生成时间: $(date)*
*项目路径: $(pwd)*
EOF

    log_success "PyCharm配置指导已生成: $pycharm_guide_file"
}

# 显示完成信息
show_completion() {
    echo ""
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${GREEN}    Cura并行开发环境安装完成！${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
    echo -e "${BLUE}项目结构:${NC}"
    echo "  $(pwd)/"
    echo "  ├── Cura/"
    echo "  │   ├── cura_venv_MacOS/    # Python虚拟环境(MacOS专用)"
    echo "  │   └── ...                 # Cura主项目文件"
    echo "  ├── Uranium/                # Uranium框架（可编辑模式）"
    echo "  ├── CuraEngine/             # CuraEngine切片引擎"
    echo "  ├── start_cura.sh           # 启动Cura"
    echo "  ├── start_cura_external.sh  # 启动Cura（外部后端）"
    echo "  └── start_curaengine.sh     # 启动CuraEngine"
    echo ""
    echo -e "${BLUE}使用方法:${NC}"
    echo "  1. 启动Cura:           ./start_cura.sh"
    echo "  2. 外部后端模式:       ./start_cura_external.sh"
    echo "  3. 启动CuraEngine:     ./start_curaengine.sh"
    echo ""
    echo -e "${BLUE}开发说明:${NC}"
    echo "  • 修改Uranium代码后，重启Cura即可看到效果"
    echo "  • 修改CuraEngine代码后，需要重新编译"
    echo "  • 使用外部后端模式可以调试CuraEngine"
    echo ""
    echo -e "${BLUE}PyCharm配置:${NC}"
    echo "  • 详细配置指导: setup_scripts/PyCharm_MacOS_配置指导.md"
    echo "  • Python解释器路径: $(pwd)/Cura/cura_venv_MacOS/bin/python"
    echo "  • 项目根目录: $(pwd)"
    echo ""
    echo -e "${GREEN}环境搭建成功！开始您的Cura开发之旅吧！${NC}"
}

# 主函数
main() {
    show_welcome
    check_project_directories
    check_system_requirements
    create_virtual_environment
    setup_conan
    setup_uranium_editable
    build_curaengine
    setup_cura_environment
    verify_setup
    create_launch_scripts
    generate_pycharm_guide
    show_completion
}

# 错误处理
trap 'log_error "脚本执行失败，请检查错误信息"; exit 1' ERR

# 运行主函数
main "$@"
