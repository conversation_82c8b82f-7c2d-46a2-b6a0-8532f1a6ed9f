# PyCharm MacOS 配置指导

## 项目结构
```
/Users/<USER>/PycharmProjects/CuraProject/
├── Cura/
│   ├── cura_venv_MacOS/        # Python虚拟环境(MacOS专用)
│   │   ├── bin/
│   │   │   ├── python          # 主要Python解释器(推荐使用)
│   │   │   └── python3.13      # 直接Python解释器
│   │   └── lib/
│   └── build/generators/       # Conan生成的环境文件
├── Uranium/                    # Uranium框架（可编辑模式）
└── CuraEngine/                 # CuraEngine切片引擎
```

## PyCharm配置步骤

### 1. 打开项目
1. 启动PyCharm
2. 选择 "Open" 或 "Open or Import"
3. 选择项目根目录: `/Users/<USER>/PycharmProjects/CuraProject`

### 2. 配置Python解释器
1. 打开 **PyCharm > Preferences** (macOS) 或 **File > Settings** (Windows/Linux)
2. 导航到 **Project > Python Interpreter**
3. 点击右上角的 **齿轮图标** > **Add...**
4. 选择 **Existing environment**
5. 设置解释器路径为:
   ```
   /Users/<USER>/PycharmProjects/CuraProject/Cura/cura_venv_MacOS/bin/python
   ```
   **注意**: 使用 `python` 而不是 `python3.13` 以避免动态库问题
6. 点击 **OK**

### 3. 配置项目根目录
1. 在 **Project > Python Interpreter** 页面
2. 点击 **Show All...** (如果有多个解释器)
3. 选择刚配置的解释器，点击右侧的 **Show paths for the selected interpreter** 图标
4. 确保以下路径已添加:
   - `/Users/<USER>/PycharmProjects/CuraProject/Cura`
   - `/Users/<USER>/PycharmProjects/CuraProject/Uranium`
   - `/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators` (如果存在)

### 4. 配置运行配置
1. 点击 **Run > Edit Configurations...**
2. 点击 **+** > **Python**
3. 配置如下:
   - **Name**: Cura Development
   - **Script path**: `/Users/<USER>/PycharmProjects/CuraProject/Cura/cura_app.py`
   - **Python interpreter**: 选择刚配置的解释器
   - **Working directory**: `/Users/<USER>/PycharmProjects/CuraProject/Cura`
   - **Environment variables**: 
     ```
     PYTHONPATH=/Users/<USER>/PycharmProjects/CuraProject/Uranium:/Users/<USER>/PycharmProjects/CuraProject/Cura
     ```

### 5. 验证配置
在PyCharm的Python Console中运行:
```python
import UM
print("Uranium路径:", UM.__file__)
# 应该显示: /Users/<USER>/PycharmProjects/CuraProject/Uranium/UM/__init__.py

import cura
print("Cura路径:", cura.__file__)
# 应该显示: /Users/<USER>/PycharmProjects/CuraProject/Cura/cura/__init__.py
```

## 常见问题解决

### 问题1: 动态库加载错误
如果遇到 `libpython3.13.dylib` 错误:
1. 使用 `/Users/<USER>/PycharmProjects/CuraProject/Cura/cura_venv_MacOS/bin/python` 而不是 `python3.13`
2. 运行修复脚本: `./setup_scripts/fix_python_interpreter.sh`

### 问题2: 模块导入错误
如果无法导入Uranium或Cura模块:
1. 检查PYTHONPATH环境变量
2. 确认Uranium可编辑模式: `conan editable list`
3. 重新激活虚拟环境: `source /Users/<USER>/PycharmProjects/CuraProject/Cura/cura_venv_MacOS/bin/activate`

### 问题3: 代码补全不工作
1. 确保PyCharm已索引完所有项目文件
2. 在 **File > Invalidate Caches and Restart** 清除缓存
3. 重新配置Python解释器

## 开发工作流

### 日常开发
1. 启动PyCharm
2. 确保使用正确的Python解释器
3. 直接在PyCharm中运行/调试Cura

### 修改Uranium代码
1. 在PyCharm中编辑 `Uranium/` 目录下的文件
2. 保存后重启Cura即可看到效果（无需重新安装）

### 修改CuraEngine代码
1. 编辑 `CuraEngine/` 目录下的文件
2. 在终端中重新编译:
   ```bash
   cd CuraEngine
   cmake --build --preset conan-release
   ```
3. 重启Cura

### 调试技巧
1. **断点调试**: 在PyCharm中设置断点，使用Debug模式运行
2. **日志查看**: 在PyCharm的Run窗口查看Cura输出
3. **变量检查**: 使用PyCharm的Variables窗口检查运行时状态

## 环境变量说明

### 虚拟环境激活
```bash
source /Users/<USER>/PycharmProjects/CuraProject/Cura/cura_venv_MacOS/bin/activate
```

### Conan环境激活
```bash
cd Cura
source build/generators/virtual_python_env.sh
```

## 故障排除

如果遇到任何问题:
1. 运行验证脚本: `./setup_scripts/verify_setup.sh`
2. 检查虚拟环境: `which python` (应该指向MacOS虚拟环境)
3. 重新运行安装脚本: `./setup_scripts/setup_all_in_one_MacOS.sh`

---
*配置生成时间: $(date)*
*项目路径: /Users/<USER>/PycharmProjects/CuraProject*
